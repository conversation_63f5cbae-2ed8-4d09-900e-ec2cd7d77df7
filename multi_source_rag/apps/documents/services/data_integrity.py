"""
Data Integrity Service for RAGSearch.

This service provides comprehensive data integrity verification and repair
functionality to ensure consistency between PostgreSQL and vector databases,
as well as across related tables in PostgreSQL.
"""

import logging
import re
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple

from apps.accounts.models import Tenant
from apps.core.models import EmbeddingModel, VectorIndex
from apps.core.utils.vectorstore import get_embedding_model, get_qdrant_client
from apps.documents.models import (ChunkRelationship, CrossPlatformReference,
                                   DocumentChunk, DocumentSource,
                                   EmbeddingMetadata, RawDocument)
from django.db import transaction
from django.db.models import Count, Exists, F, OuterRef, Q, Subquery

logger = logging.getLogger(__name__)


class DataIntegrityService:
    """
    Service for verifying and repairing data integrity across databases.

    This service provides methods to:
    1. Verify consistency between PostgreSQL and vector databases
    2. Check for orphaned or missing records across related tables
    3. Repair inconsistencies automatically when possible
    4. Generate detailed reports on data integrity issues
    """

    def __init__(self, tenant: Tenant):
        """
        Initialize the data integrity service.

        Args:
            tenant: The tenant to check data integrity for
        """
        self.tenant = tenant
        self.qdrant_client = None
        self.embedding_model = None
        self.vector_index = None

    def initialize_vector_resources(self) -> None:
        """
        Initialize vector database resources.
        """
        # Get default embedding model
        model = EmbeddingModel.objects.filter(
            tenant=self.tenant, is_default=True
        ).first()

        if not model:
            raise ValueError(
                f"No default embedding model found for tenant {self.tenant.name}"
            )

        # Get default vector index
        vector_index = VectorIndex.objects.filter(
            tenant=self.tenant, is_active=True
        ).first()

        if not vector_index:
            raise ValueError(
                f"No active vector index found for tenant {self.tenant.name}"
            )

        # Initialize Qdrant client
        self.qdrant_client = get_qdrant_client()
        self.embedding_model = get_embedding_model(model.model_name)
        self.vector_index = vector_index

    def verify_data_integrity(self, source_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Verify data integrity across databases and tables.

        Args:
            source_id: Optional source ID to limit verification to

        Returns:
            Dict containing verification results and statistics
        """
        logger.info(
            f"Starting data integrity verification for tenant {self.tenant.name}"
        )
        start_time = time.time()

        # Initialize vector resources
        try:
            self.initialize_vector_resources()
        except Exception as e:
            logger.error(f"Failed to initialize vector resources: {e}")
            return {
                "status": "failed",
                "error": f"Failed to initialize vector resources: {str(e)}",
                "duration_seconds": time.time() - start_time,
            }

        # Build source filter
        source_filter = {}
        if source_id:
            source_filter = {"id": source_id}

        # Get all sources for the tenant
        sources = DocumentSource.objects.filter(tenant=self.tenant, **source_filter)

        # Initialize results
        results = {
            "status": "success",
            "tenant": self.tenant.name,
            "tenant_id": str(self.tenant.id),
            "sources_checked": sources.count(),
            "issues": {
                "chunks_without_embeddings": 0,
                "orphaned_embeddings": 0,
                "missing_vector_entries": 0,
                "orphaned_vector_entries": 0,
                "broken_relationships": 0,
                "orphaned_references": 0,
                "documents_without_chunks": 0,
            },
            "source_details": [],
            "duration_seconds": 0,
        }

        # Check each source
        for source in sources:
            source_result = self._verify_source_integrity(source)
            results["source_details"].append(source_result)

            # Aggregate issues
            for issue_type, count in source_result["issues"].items():
                results["issues"][issue_type] += count

        # Calculate duration
        results["duration_seconds"] = time.time() - start_time

        logger.info(
            f"Data integrity verification completed in {results['duration_seconds']:.2f} seconds"
        )
        return results

    def _verify_source_integrity(self, source: DocumentSource) -> Dict[str, Any]:
        """
        Verify data integrity for a specific source.

        Args:
            source: The document source to verify

        Returns:
            Dict containing verification results for the source
        """
        logger.info(f"Verifying data integrity for source {source.name}")

        # Initialize source results
        source_result = {
            "source_name": source.name,
            "source_id": str(source.id),
            "source_type": source.source_type,
            "documents_checked": 0,
            "chunks_checked": 0,
            "issues": {
                "chunks_without_embeddings": 0,
                "orphaned_embeddings": 0,
                "missing_vector_entries": 0,
                "orphaned_vector_entries": 0,
                "broken_relationships": 0,
                "orphaned_references": 0,
                "documents_without_chunks": 0,
            },
            "details": [],
        }

        # Get all documents for the source
        documents = RawDocument.objects.filter(tenant=self.tenant, source=source)
        source_result["documents_checked"] = documents.count()

        # Check documents without chunks
        docs_without_chunks = documents.annotate(chunk_count=Count("chunks")).filter(
            chunk_count=0
        )

        source_result["issues"][
            "documents_without_chunks"
        ] = docs_without_chunks.count()

        # Get all chunks for the source
        chunks = DocumentChunk.objects.filter(
            tenant=self.tenant, document__source=source
        )
        source_result["chunks_checked"] = chunks.count()

        # Check for chunks without embeddings
        chunks_without_embeddings = chunks.exclude(
            id__in=EmbeddingMetadata.objects.values_list("chunk_id", flat=True)
        )
        source_result["issues"][
            "chunks_without_embeddings"
        ] = chunks_without_embeddings.count()

        # Check for orphaned embeddings
        orphaned_embeddings = EmbeddingMetadata.objects.filter(
            chunk__tenant=self.tenant, chunk__document__source=source
        ).exclude(chunk_id__in=chunks.values_list("id", flat=True))
        source_result["issues"]["orphaned_embeddings"] = orphaned_embeddings.count()

        # Check for broken relationships
        broken_relationships = ChunkRelationship.objects.filter(
            Q(tenant=self.tenant)
            & (
                Q(source_chunk__document__source=source)
                | Q(target_chunk__document__source=source)
            )
        ).filter(Q(source_chunk__isnull=True) | Q(target_chunk__isnull=True))
        source_result["issues"]["broken_relationships"] = broken_relationships.count()

        # Check for orphaned references
        orphaned_references = CrossPlatformReference.objects.filter(
            tenant=self.tenant, source_chunk__document__source=source
        ).filter(source_chunk__isnull=True)
        source_result["issues"]["orphaned_references"] = orphaned_references.count()

        # Check vector database consistency
        if self.qdrant_client and self.vector_index:
            try:
                # Get vector IDs from PostgreSQL
                vector_ids = set(
                    EmbeddingMetadata.objects.filter(
                        chunk__tenant=self.tenant, chunk__document__source=source
                    ).values_list("vector_id", flat=True)
                )

                # Get chunk IDs with embeddings
                chunk_ids_with_embeddings = set(
                    EmbeddingMetadata.objects.filter(
                        chunk__tenant=self.tenant, chunk__document__source=source
                    ).values_list("chunk_id", flat=True)
                )

                # Check for missing vector entries
                # This is a simplified check - in production, you'd need to batch this
                # for large datasets to avoid memory issues
                missing_vectors = 0
                for vector_id in vector_ids:
                    try:
                        # Check if vector exists in Qdrant
                        points = self.qdrant_client.retrieve(
                            collection_name=self.vector_index.collection_name,
                            ids=[vector_id],
                        )
                        if not points:
                            missing_vectors += 1
                    except Exception as e:
                        logger.error(f"Error checking vector {vector_id}: {e}")
                        missing_vectors += 1

                source_result["issues"]["missing_vector_entries"] = missing_vectors

            except Exception as e:
                logger.error(f"Error checking vector database consistency: {e}")
                source_result["vector_check_error"] = str(e)

        return source_result

    def repair_data_integrity(
        self, source_id: Optional[int] = None, auto_repair: bool = False
    ) -> Dict[str, Any]:
        """
        Repair data integrity issues.

        Args:
            source_id: Optional source ID to limit repair to
            auto_repair: Whether to automatically repair issues

        Returns:
            Dict containing repair results
        """
        # First verify to identify issues
        verification = self.verify_data_integrity(source_id)

        if verification["status"] != "success":
            return {
                "status": "failed",
                "error": verification.get("error", "Verification failed"),
                "verification": verification,
            }

        # Initialize repair results
        repair_results = {
            "status": "success",
            "tenant": self.tenant.name,
            "tenant_id": str(self.tenant.id),
            "auto_repair": auto_repair,
            "repaired": {
                "chunks_without_embeddings": 0,
                "orphaned_embeddings": 0,
                "broken_relationships": 0,
                "orphaned_references": 0,
                "documents_without_chunks": 0,
                "chunks_without_technical_entities": 0,
                "chunks_without_quality_scores": 0,
            },
            "source_details": [],
            "duration_seconds": 0,
        }

        start_time = time.time()

        # If auto_repair is False, just return the verification results
        if not auto_repair:
            repair_results["verification"] = verification
            repair_results["message"] = "Dry run only, no repairs performed"
            repair_results["duration_seconds"] = time.time() - start_time
            return repair_results

        # Process each source
        for source_detail in verification["source_details"]:
            source_id = source_detail["source_id"]
            source = DocumentSource.objects.get(id=source_id)

            source_repair = self._repair_source_integrity(source, source_detail)
            repair_results["source_details"].append(source_repair)

            # Aggregate repairs
            for issue_type, count in source_repair["repaired"].items():
                repair_results["repaired"][issue_type] += count

        # Calculate duration
        repair_results["duration_seconds"] = time.time() - start_time

        logger.info(
            f"Data integrity repair completed in {repair_results['duration_seconds']:.2f} seconds"
        )
        return repair_results

    def _repair_source_integrity(
        self, source: DocumentSource, source_detail: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Repair data integrity for a specific source.

        Args:
            source: The document source to repair
            source_detail: The verification details for the source

        Returns:
            Dict containing repair results for the source
        """
        logger.info(f"Repairing data integrity for source {source.name}")

        # Initialize source repair results
        source_repair = {
            "source_name": source.name,
            "source_id": str(source.id),
            "source_type": source.source_type,
            "repaired": {
                "chunks_without_embeddings": 0,
                "orphaned_embeddings": 0,
                "broken_relationships": 0,
                "orphaned_references": 0,
                "documents_without_chunks": 0,
            },
            "errors": [],
        }

        # 1. Repair chunks without embeddings
        if source_detail["issues"]["chunks_without_embeddings"] > 0:
            repaired = self._repair_chunks_without_embeddings(source)
            source_repair["repaired"]["chunks_without_embeddings"] = repaired

        # 2. Remove orphaned embeddings
        if source_detail["issues"]["orphaned_embeddings"] > 0:
            repaired = self._repair_orphaned_embeddings(source)
            source_repair["repaired"]["orphaned_embeddings"] = repaired

        # 3. Remove broken relationships
        if source_detail["issues"]["broken_relationships"] > 0:
            repaired = self._repair_broken_relationships(source)
            source_repair["repaired"]["broken_relationships"] = repaired

        # 4. Remove orphaned references
        if source_detail["issues"]["orphaned_references"] > 0:
            repaired = self._repair_orphaned_references(source)
            source_repair["repaired"]["orphaned_references"] = repaired

        # 5. Process documents without chunks
        if source_detail["issues"]["documents_without_chunks"] > 0:
            repaired = self._repair_documents_without_chunks(source)
            source_repair["repaired"]["documents_without_chunks"] = repaired

        return source_repair

    def _repair_chunks_without_embeddings(self, source: DocumentSource) -> int:
        """
        Repair chunks without embeddings by creating embeddings for them.

        Args:
            source: The document source to repair

        Returns:
            int: Number of chunks repaired
        """
        from apps.documents.services.llama_ingestion_service_unified import IngestionService

        # Get chunks without embeddings
        chunks_without_embeddings = DocumentChunk.objects.filter(
            tenant=self.tenant, document__source=source
        ).exclude(id__in=EmbeddingMetadata.objects.values_list("chunk_id", flat=True))

        # Initialize ingestion service for embedding creation
        ingestion_service = IngestionService(self.tenant)

        # Create embeddings for chunks
        repaired_count = 0
        for chunk in chunks_without_embeddings:
            try:
                embedding = ingestion_service._create_embedding(chunk)
                if embedding:
                    repaired_count += 1
                    logger.info(f"Created embedding for chunk {chunk.id}")
            except Exception as e:
                logger.error(f"Error creating embedding for chunk {chunk.id}: {e}")

        return repaired_count

    def _repair_orphaned_embeddings(self, source: DocumentSource) -> int:
        """
        Repair orphaned embeddings by removing them.

        Args:
            source: The document source to repair

        Returns:
            int: Number of orphaned embeddings removed
        """
        # Get orphaned embeddings
        orphaned_embeddings = EmbeddingMetadata.objects.filter(
            chunk__tenant=self.tenant, chunk__document__source=source
        ).exclude(
            chunk_id__in=DocumentChunk.objects.filter(
                tenant=self.tenant, document__source=source
            ).values_list("id", flat=True)
        )

        # Get vector IDs to remove from vector database
        vector_ids = list(orphaned_embeddings.values_list("vector_id", flat=True))

        # Remove from vector database
        if vector_ids and self.qdrant_client and self.vector_index:
            try:
                # Delete in batches to avoid overwhelming the vector database
                batch_size = 100
                for i in range(0, len(vector_ids), batch_size):
                    batch = vector_ids[i : i + batch_size]
                    self.qdrant_client.delete(
                        collection_name=self.vector_index.collection_name,
                        points_selector=batch,
                    )
                    logger.info(f"Deleted {len(batch)} vectors from Qdrant")
            except Exception as e:
                logger.error(f"Error deleting vectors from Qdrant: {e}")

        # Count and delete orphaned embeddings
        count = orphaned_embeddings.count()
        orphaned_embeddings.delete()

        logger.info(f"Deleted {count} orphaned embeddings")
        return count

    def _repair_broken_relationships(self, source: DocumentSource) -> int:
        """
        Repair broken relationships by removing them.

        Args:
            source: The document source to repair

        Returns:
            int: Number of broken relationships removed
        """
        # Get broken relationships
        broken_relationships = ChunkRelationship.objects.filter(
            Q(tenant=self.tenant)
            & (
                Q(source_chunk__document__source=source)
                | Q(target_chunk__document__source=source)
            )
        ).filter(Q(source_chunk__isnull=True) | Q(target_chunk__isnull=True))

        # Count and delete broken relationships
        count = broken_relationships.count()
        broken_relationships.delete()

        logger.info(f"Deleted {count} broken relationships")
        return count

    def _repair_orphaned_references(self, source: DocumentSource) -> int:
        """
        Repair orphaned references by removing them.

        Args:
            source: The document source to repair

        Returns:
            int: Number of orphaned references removed
        """
        # Get orphaned references
        orphaned_references = CrossPlatformReference.objects.filter(
            tenant=self.tenant, source_chunk__document__source=source
        ).filter(source_chunk__isnull=True)

        # Count and delete orphaned references
        count = orphaned_references.count()
        orphaned_references.delete()

        logger.info(f"Deleted {count} orphaned references")
        return count

    def _repair_documents_without_chunks(self, source: DocumentSource) -> int:
        """
        Repair documents without chunks by reprocessing them.
        If the document can't be retrieved from the source interface, it will be deleted.

        Args:
            source: The document source to repair

        Returns:
            int: Number of documents repaired
        """
        from apps.documents.services.llama_ingestion_service_unified import IngestionService

        # Get documents without chunks
        docs_without_chunks = (
            RawDocument.objects.filter(tenant=self.tenant, source=source)
            .annotate(chunk_count=Count("chunks"))
            .filter(chunk_count=0)
        )

        # Initialize ingestion service
        ingestion_service = IngestionService(self.tenant)

        # Reprocess documents
        repaired_count = 0
        for doc in docs_without_chunks:
            try:
                # Get document data from source interface
                source_interface = ingestion_service._get_source_interface(source)
                document_data = source_interface.get_document(doc.external_id)

                # Process document
                ingestion_service._process_document(source, document_data)
                repaired_count += 1
                logger.info(f"Reprocessed document {doc.id}")
            except Exception as e:
                logger.error(f"Error reprocessing document {doc.id}: {e}")

                # If we can't reprocess the document, delete it to maintain data integrity
                try:
                    logger.warning(
                        f"Deleting document {doc.id} that couldn't be reprocessed"
                    )
                    doc.delete()
                    repaired_count += 1
                except Exception as delete_error:
                    logger.error(f"Error deleting document {doc.id}: {delete_error}")

        return repaired_count

    def _repair_chunks_without_technical_entities(self, source: DocumentSource) -> int:
        """
        Repair chunks without technical entities by extracting them.

        Args:
            source: The document source to repair

        Returns:
            int: Number of chunks repaired
        """
        from apps.documents.cleaners.technical_entity_extractor import \
            TechnicalEntityExtractor

        # Get chunks without technical entities or with empty technical entities
        chunks_without_entities = DocumentChunk.objects.filter(
            document__tenant=self.tenant, document__source=source
        ).filter(Q(technical_entities__isnull=True) | Q(technical_entities={}))

        # Initialize entity extractor
        entity_extractor = TechnicalEntityExtractor()

        # Process chunks in batches
        batch_size = 100
        total_chunks = chunks_without_entities.count()
        repaired_count = 0

        for i in range(0, total_chunks, batch_size):
            batch = chunks_without_entities[i : i + batch_size]

            for chunk in batch:
                try:
                    # Extract technical entities
                    technical_entities = entity_extractor.extract_entities(chunk.text)

                    # Update chunk
                    chunk.technical_entities = technical_entities
                    chunk.save(update_fields=["technical_entities"])

                    repaired_count += 1

                    if repaired_count % 100 == 0:
                        logger.info(
                            f"Repaired {repaired_count}/{total_chunks} chunks without technical entities"
                        )

                except Exception as e:
                    logger.error(
                        f"Error extracting technical entities for chunk {chunk.id}: {e}"
                    )

        logger.info(f"Repaired {repaired_count} chunks without technical entities")
        return repaired_count

    def _repair_chunks_without_quality_scores(self, source: DocumentSource) -> int:
        """
        Repair chunks without quality scores by calculating them.

        Args:
            source: The document source to repair

        Returns:
            int: Number of chunks repaired
        """
        # Get chunks without quality scores
        chunks_without_scores = DocumentChunk.objects.filter(
            document__tenant=self.tenant, document__source=source, quality_score=0
        )

        # Process chunks in batches
        batch_size = 100
        total_chunks = chunks_without_scores.count()
        repaired_count = 0

        for i in range(0, total_chunks, batch_size):
            batch = chunks_without_scores[i : i + batch_size]

            for chunk in batch:
                try:
                    # Calculate quality score based on content
                    quality_score = self._calculate_quality_score(chunk)

                    # Update chunk
                    chunk.quality_score = quality_score
                    chunk.is_high_quality = quality_score >= 0.7
                    chunk.save(update_fields=["quality_score", "is_high_quality"])

                    repaired_count += 1

                    if repaired_count % 100 == 0:
                        logger.info(
                            f"Repaired {repaired_count}/{total_chunks} chunks without quality scores"
                        )

                except Exception as e:
                    logger.error(
                        f"Error calculating quality score for chunk {chunk.id}: {e}"
                    )

        logger.info(f"Repaired {repaired_count} chunks without quality scores")
        return repaired_count

    def _calculate_quality_score(self, chunk: DocumentChunk) -> float:
        """
        Calculate quality score for a document chunk.

        Args:
            chunk: Document chunk to calculate score for

        Returns:
            float: Quality score (0-1)
        """
        text = chunk.text

        if not text:
            return 0.0

        # Calculate base scores
        length_score = self._calculate_length_score(text)
        technical_score = self._calculate_technical_score(text)
        structure_score = self._calculate_structure_score(text)

        # Calculate weighted score
        quality_score = (
            length_score * 0.4 + technical_score * 0.4 + structure_score * 0.2
        )

        # Round to 2 decimal places
        return round(quality_score, 2)

    def _calculate_length_score(self, text: str) -> float:
        """
        Calculate score based on text length.

        Args:
            text: Text to score

        Returns:
            float: Length score (0-1)
        """
        # Count words
        words = text.split()
        word_count = len(words)

        # Score based on word count
        if word_count < 5:
            return 0.1  # Very short texts
        elif word_count < 10:
            return 0.3  # Short texts
        elif word_count < 25:
            return 0.6  # Medium-length texts
        elif word_count < 50:
            return 0.8  # Good length
        else:
            return 1.0  # Detailed texts

    def _calculate_technical_score(self, text: str) -> float:
        """
        Calculate score based on technical content.

        Args:
            text: Text to score

        Returns:
            float: Technical content score (0-1)
        """
        score = 0.5  # Start with neutral score

        # Check for code blocks
        if "```" in text:
            score += 0.3

        # Check for inline code
        if "`" in text:
            score += 0.1

        # Check for technical terms
        tech_terms = [
            "api",
            "function",
            "method",
            "class",
            "object",
            "variable",
            "database",
            "query",
            "sql",
            "schema",
            "table",
            "index",
            "http",
            "rest",
            "graphql",
            "endpoint",
            "request",
            "response",
            "error",
            "exception",
            "bug",
            "fix",
            "issue",
            "problem",
            "deploy",
            "release",
            "version",
            "update",
            "upgrade",
            "test",
            "unit test",
            "integration test",
            "e2e test",
        ]

        term_count = 0
        for term in tech_terms:
            if re.search(r"\b" + re.escape(term) + r"\b", text, re.IGNORECASE):
                term_count += 1

        if term_count > 0:
            score += min(0.05 * term_count, 0.3)  # Cap at 0.3

        # Check for URLs
        urls = re.findall(r'https?://[^\s<>"{}|\\^`\[\]]+', text)
        if urls:
            score += min(0.05 * len(urls), 0.2)  # Cap at 0.2

        # Cap at 1.0
        return min(score, 1.0)

    def _calculate_structure_score(self, text: str) -> float:
        """
        Calculate score based on text structure.

        Args:
            text: Text to score

        Returns:
            float: Structure score (0-1)
        """
        score = 0.5  # Start with neutral score

        # Check for lists
        list_items = re.findall(r"(?:^|\n)\s*[-*•]\s+", text)
        if list_items:
            score += min(0.05 * len(list_items), 0.2)  # Cap at 0.2

        # Check for numbered lists
        numbered_items = re.findall(r"(?:^|\n)\s*\d+\.\s+", text)
        if numbered_items:
            score += min(0.05 * len(numbered_items), 0.2)  # Cap at 0.2

        # Check for headings (Markdown style)
        headings = re.findall(r"(?:^|\n)\s*#{1,3}\s+", text)
        if headings:
            score += 0.2

        # Check for paragraphs
        paragraphs = text.split("\n\n")
        if len(paragraphs) > 1:
            score += min(0.05 * len(paragraphs), 0.2)  # Cap at 0.2

        # Check for formatting (bold, italic)
        formatting = re.findall(r"\*\*.*?\*\*|\*.*?\*|_.*?_|~.*?~", text)
        if formatting:
            score += 0.1

        # Cap at 1.0
        return min(score, 1.0)

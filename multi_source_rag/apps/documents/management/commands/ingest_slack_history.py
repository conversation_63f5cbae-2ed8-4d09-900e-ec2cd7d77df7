"""
Management command to ingest historical data from <PERSON>lack with pagination support.
"""

import time
from datetime import datetime, timedelta

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, RawDocument
from apps.documents.services.llama_ingestion_service_unified import IngestionService
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone


class Command(BaseCommand):
    help = "Ingest historical data from Slack with pagination support"

    def add_arguments(self, parser):
        parser.add_argument(
            "--tenant-slug", type=str, required=True, help="Tenant slug"
        )
        parser.add_argument(
            "--channel", type=str, required=True, help="Slack channel ID"
        )
        parser.add_argument(
            "--months",
            type=int,
            default=24,
            help="Number of months to fetch (default: 24)",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=200,
            help="Batch size for pagination (default: 200)",
        )
        parser.add_argument(
            "--rate-limit",
            type=float,
            default=1.0,
            help="Rate limit delay in seconds (default: 1.0)",
        )
        parser.add_argument(
            "--username", type=str, help="Username to associate with the ingestion job"
        )
        parser.add_argument(
            "--token", type=str, help="Slack API token (if not configured in source)"
        )
        parser.add_argument(
            "--resume", action="store_true", help="Resume from last ingested timestamp"
        )
        parser.add_argument(
            "--dry-run", action="store_true", help="Dry run (no actual ingestion)"
        )

    def handle(self, *args, **options):
        tenant_slug = options["tenant_slug"]
        channel_id = options["channel"]
        months = options["months"]
        batch_size = options["batch_size"]
        rate_limit = options["rate_limit"]
        username = options["username"]
        token = options["token"]
        resume = options["resume"]
        dry_run = options["dry_run"]

        try:
            # Get tenant
            try:
                tenant = Tenant.objects.get(slug=tenant_slug)
                self.stdout.write(f"Found tenant: {tenant.name}")
            except Tenant.DoesNotExist:
                raise CommandError(f"Tenant with slug {tenant_slug} not found")

            # Get user if username is provided
            user = None
            if username:
                try:
                    user = User.objects.get(username=username)
                    self.stdout.write(f"Found user: {user.username}")
                except User.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f"User not found: {username}"))

            # Get Slack document source
            try:
                source = DocumentSource.objects.get(
                    tenant=tenant, source_type="slack", is_active=True
                )
                self.stdout.write(f"Found Slack document source: {source.name}")

                # Update token if provided
                if token:
                    config = source.config
                    config["api_token"] = token
                    source.config = config
                    source.save()
                    self.stdout.write("Updated token in source configuration")

            except DocumentSource.DoesNotExist:
                if not token:
                    raise CommandError("Slack API token is required for new sources")

                # Create new source
                source = DocumentSource.objects.create(
                    tenant=tenant,
                    name="Slack Integration",
                    source_type="slack",
                    config={"api_token": token, "channel_ids": [channel_id]},
                    is_active=True,
                )
                self.stdout.write(f"Created new Slack document source: {source.name}")
            except DocumentSource.MultipleObjectsReturned:
                # If multiple sources exist, use the first one
                source = DocumentSource.objects.filter(
                    tenant=tenant, source_type="slack", is_active=True
                ).first()
                self.stdout.write(f"Multiple Slack sources found, using: {source.name}")

                # Update token if provided
                if token:
                    config = source.config
                    config["api_token"] = token
                    source.config = config
                    source.save()
                    self.stdout.write("Updated token in source configuration")

            # Create ingestion service
            ingestion_service = IngestionService(tenant, user)

            # Create processing job
            job = ingestion_service.create_processing_job(source)
            self.stdout.write(f"Created processing job: {job.id}")

            # Calculate oldest timestamp to fetch
            oldest_time = timezone.now() - timedelta(days=30 * months)
            oldest_ts = oldest_time.timestamp()

            # If resuming, find the oldest message already ingested
            latest_ts = None
            if resume:
                latest_doc = (
                    RawDocument.objects.filter(
                        tenant=tenant, source=source, metadata__channel_id=channel_id
                    )
                    .order_by("-created_at")
                    .first()
                )

                if latest_doc and "ts" in latest_doc.metadata:
                    latest_ts = latest_doc.metadata["ts"]
                    self.stdout.write(f"Resuming from timestamp: {latest_ts}")
                else:
                    self.stdout.write(
                        "No previous documents found, starting from scratch"
                    )

            # Process source with pagination
            self.stdout.write(
                f"Ingesting data from channel: {channel_id} for the last {months} months"
            )
            self.stdout.write(
                f"Using batch size: {batch_size}, rate limit: {rate_limit}s"
            )

            if dry_run:
                self.stdout.write(
                    self.style.WARNING("DRY RUN MODE - No documents will be ingested")
                )

            # Set up pagination parameters
            total_processed = 0
            total_failed = 0
            cursor = None
            has_more = True
            page_count = 0

            while has_more:
                page_count += 1
                self.stdout.write(f"Processing page {page_count}...")

                # Process batch
                if not dry_run:
                    processed, failed = ingestion_service.process_source(
                        source,
                        job,
                        channel_id=channel_id,
                        oldest=str(oldest_ts),
                        latest=latest_ts,
                        limit=batch_size,
                        cursor=cursor,
                    )

                    total_processed += processed
                    total_failed += failed

                    self.stdout.write(
                        f"Page {page_count}: Processed {processed} documents, {failed} failures"
                    )
                else:
                    self.stdout.write(
                        f"Page {page_count}: Would process up to {batch_size} documents"
                    )
                    # Simulate some processing in dry run mode
                    processed = batch_size

                # Check if we need to continue pagination
                if processed < batch_size:
                    has_more = False
                    self.stdout.write("Reached end of available messages")
                else:
                    # Get cursor for next page
                    cursor = ingestion_service.get_pagination_cursor()

                    if not cursor:
                        has_more = False
                        self.stdout.write(
                            "No pagination cursor returned, ending pagination"
                        )
                    else:
                        self.stdout.write(f"Got cursor for next page: {cursor[:20]}...")

                        # Apply rate limiting
                        self.stdout.write(
                            f"Rate limiting: sleeping for {rate_limit} seconds"
                        )
                        time.sleep(rate_limit)

            if not dry_run:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully processed {total_processed} documents with {total_failed} failures"
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Dry run completed. Would have processed approximately {page_count * batch_size} documents"
                    )
                )

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error ingesting Slack data: {str(e)}"))
            raise CommandError(f"Error ingesting Slack data: {str(e)}")

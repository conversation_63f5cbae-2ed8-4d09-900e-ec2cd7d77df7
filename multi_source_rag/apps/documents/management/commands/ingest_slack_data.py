"""
Management command to ingest data from Slack.
"""

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource
from apps.documents.services.llama_ingestion_service_unified import IngestionService
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Ingest data from Slack"

    def add_arguments(self, parser):
        parser.add_argument(
            "--tenant-slug", type=str, required=True, help="Tenant slug"
        )
        parser.add_argument(
            "--channel", type=str, required=True, help="Slack channel ID or name"
        )
        parser.add_argument(
            "--days", type=int, default=7, help="Number of days to fetch"
        )
        parser.add_argument(
            "--username", type=str, help="Username to associate with the ingestion job"
        )

    def handle(self, *args, **options):
        tenant_slug = options["tenant_slug"]
        channel = options["channel"]
        days = options["days"]
        username = options["username"]

        try:
            # Get tenant
            tenant = Tenant.objects.get(slug=tenant_slug)
            self.stdout.write(f"Found tenant: {tenant.name}")

            # Get user if username is provided
            user = None
            if username:
                try:
                    user = User.objects.get(username=username)
                    self.stdout.write(f"Found user: {user.username}")
                except User.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f"User not found: {username}"))

            # Get Slack document source
            try:
                source = DocumentSource.objects.get(
                    tenant=tenant, source_type="slack", is_active=True
                )
                self.stdout.write(f"Found Slack document source: {source.name}")
            except DocumentSource.DoesNotExist:
                self.stdout.write(self.style.ERROR("Slack document source not found"))
                self.stdout.write("Please run setup_slack_ingestion command first")
                return
            except DocumentSource.MultipleObjectsReturned:
                # If multiple sources exist, use the first one
                source = DocumentSource.objects.filter(
                    tenant=tenant, source_type="slack", is_active=True
                ).first()
                self.stdout.write(f"Multiple Slack sources found, using: {source.name}")

            # Create ingestion service
            ingestion_service = IngestionService(tenant, user)

            # Create processing job
            job = ingestion_service.create_processing_job(source)
            self.stdout.write(f"Created processing job: {job.id}")

            # Process source
            self.stdout.write(
                f"Ingesting data from channel: {channel} for the last {days} days"
            )
            print(
                f"DEBUG: Starting ingestion from channel {channel} for the last {days} days"
            )
            processed, failed = ingestion_service.process_source(
                source, job, channel_id=channel, days=days
            )
            print(
                f"DEBUG: Finished ingestion with {processed} processed and {failed} failed"
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully processed {processed} documents with {failed} failures"
                )
            )

        except Tenant.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"Tenant not found: {tenant_slug}"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error: {e}"))

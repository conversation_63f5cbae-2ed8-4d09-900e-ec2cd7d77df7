"""
Management command to ingest data from GitHub.
"""

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource
from apps.documents.services.llama_ingestion_service_unified import IngestionService
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand, CommandError


class Command(BaseCommand):
    help = "Ingest data from GitHub"

    def add_arguments(self, parser):
        parser.add_argument(
            "--tenant-slug", type=str, required=True, help="Tenant slug"
        )
        parser.add_argument(
            "--repo",
            type=str,
            required=True,
            help="Repository name (format: owner/repo)",
        )
        parser.add_argument(
            "--days", type=int, default=30, help="Number of days to fetch"
        )
        parser.add_argument(
            "--content-types",
            type=str,
            default="pull_request,issue",
            help="Types of content to fetch (comma-separated)",
        )
        parser.add_argument(
            "--state",
            type=str,
            default="all",
            help="State of PRs/issues to fetch (open, closed, all)",
        )
        parser.add_argument(
            "--username", type=str, help="Username to associate with the ingestion job"
        )
        parser.add_argument(
            "--token", type=str, help="GitHub token (if not configured in source)"
        )

    def handle(self, *args, **options):
        tenant_slug = options["tenant_slug"]
        repo = options["repo"]
        days = options["days"]
        content_types = options["content_types"].split(",")
        state = options["state"]
        username = options.get("username")
        token = options.get("token")

        try:
            # Get tenant
            try:
                tenant = Tenant.objects.get(slug=tenant_slug)
                self.stdout.write(f"Found tenant: {tenant.name}")
            except Tenant.DoesNotExist:
                raise CommandError(f"Tenant with slug {tenant_slug} not found")

            # Get user if username is provided
            user = None
            if username:
                try:
                    user = User.objects.get(username=username)
                    self.stdout.write(f"Found user: {user.username}")
                except User.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f"User not found: {username}"))

            # Parse repository
            try:
                owner, repo_name = repo.split("/")
            except ValueError:
                raise CommandError(
                    f"Invalid repository format: {repo}. Expected format: owner/repo"
                )

            # Get or create source
            source_name = f"GitHub: {repo}"
            try:
                source = DocumentSource.objects.get(tenant=tenant, name=source_name)
                self.stdout.write(f"Found existing source: {source.name}")

                # Update token if provided
                if token:
                    config = source.config
                    config["token"] = token
                    source.config = config
                    source.save()
                    self.stdout.write("Updated token in source configuration")

            except DocumentSource.DoesNotExist:
                # Create new source
                if not token:
                    raise CommandError("GitHub token is required for new sources")

                source = DocumentSource.objects.create(
                    tenant=tenant,
                    name=source_name,
                    source_type="github",
                    config={"token": token, "owner": owner, "repo": repo_name},
                )
                self.stdout.write(f"Created new source: {source.name}")

            # Create ingestion service
            ingestion_service = IngestionService(tenant, user)

            # Create processing job
            job = ingestion_service.create_processing_job(source)
            self.stdout.write(f"Created processing job: {job.id}")

            # Process source
            self.stdout.write(
                f"Ingesting data from repository: {repo} for the last {days} days"
            )
            print(
                f"DEBUG: Starting ingestion from repository {repo} for the last {days} days"
            )
            processed, failed = ingestion_service.process_source(
                source, job, days=days, content_types=content_types, state=state
            )
            print(
                f"DEBUG: Finished ingestion with {processed} processed and {failed} failed"
            )

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully processed {processed} documents with {failed} failures"
                )
            )

        except Exception as e:
            raise CommandError(f"Error ingesting GitHub data: {str(e)}")

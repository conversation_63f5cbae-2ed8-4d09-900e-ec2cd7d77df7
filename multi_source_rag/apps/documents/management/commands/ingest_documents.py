"""
Management command to ingest documents from a source.
"""

import time
import traceback

from apps.accounts.models import Tenant
from apps.documents.models import DocumentProcessingJob, DocumentSource
from apps.documents.services.llama_ingestion_service_unified import IngestionService
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand, CommandError
from django.db import DatabaseError, IntegrityError, transaction
from django.utils import timezone


class Command(BaseCommand):
    help = "Ingest documents from a source"

    def add_arguments(self, parser):
        parser.add_argument("source_id", type=int, help="ID of the document source")
        parser.add_argument("--tenant", type=str, help="Tenant slug")
        parser.add_argument(
            "--user", type=str, help="Username of the user to attribute the job to"
        )
        parser.add_argument(
            "--retry",
            type=int,
            default=3,
            help="Number of retries for failed operations",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Batch size for document processing",
        )
        parser.add_argument(
            "--params",
            nargs="*",
            help="Additional parameters for the source in key=value format",
        )

    def handle(self, *args, **options):
        source_id = options["source_id"]
        tenant_slug = options.get("tenant", "default")
        username = options.get("user", "admin")
        retry_count = options.get("retry", 3)
        batch_size = options.get("batch_size", 100)

        # Parse additional parameters
        params = {}
        if options.get("params"):
            for param in options.get("params"):
                if "=" in param:
                    key, value = param.split("=", 1)
                    params[key] = value

        try:
            # Get the tenant
            tenant = Tenant.objects.get(slug=tenant_slug)

            # Get the source
            source = DocumentSource.objects.get(id=source_id, tenant=tenant)

            # Get the user
            user = User.objects.get(username=username)

            # Create a processing job
            job = self._create_processing_job(tenant, source, user)

            self.stdout.write(
                f"Created processing job {job.id} for source {source.name}"
            )

            # Create ingestion service
            ingestion_service = IngestionService(tenant, user)

            # Process the source with retries
            processed, failed = 0, 0
            retry_attempts = 0

            while retry_attempts < retry_count:
                try:
                    # Update job status to processing
                    job.status = "processing"
                    job.started_at = timezone.now()
                    job.save()

                    # Process the source
                    processed, failed = ingestion_service.process_source(
                        source, job, batch_size=batch_size, **params
                    )

                    # Update job status to completed
                    job.status = "completed"
                    job.completed_at = timezone.now()
                    job.documents_processed = processed
                    job.documents_failed = failed
                    job.save()

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Processing job {job.id} completed: {processed} processed, {failed} failed"
                        )
                    )

                    # Break out of retry loop on success
                    break

                except (IntegrityError, DatabaseError) as e:
                    # Database-related errors that might be transient
                    retry_attempts += 1
                    if retry_attempts >= retry_count:
                        # Update job status to failed
                        job.status = "failed"
                        job.error_message = (
                            f"Database error after {retry_count} attempts: {str(e)}"
                        )
                        job.completed_at = timezone.now()
                        job.save()

                        self.stdout.write(
                            self.style.ERROR(
                                f"Processing job {job.id} failed after {retry_count} attempts: {str(e)}"
                            )
                        )
                        raise CommandError(f"Database error: {str(e)}")

                    # Wait before retrying
                    wait_time = 2**retry_attempts  # Exponential backoff
                    self.stdout.write(
                        self.style.WARNING(
                            f"Database error, retrying in {wait_time} seconds (attempt {retry_attempts}/{retry_count}): {str(e)}"
                        )
                    )
                    time.sleep(wait_time)

                except Exception as e:
                    # Other errors
                    error_traceback = traceback.format_exc()

                    # Update job status to failed
                    job.status = "failed"
                    job.error_message = f"Error: {str(e)}\n{error_traceback}"
                    job.completed_at = timezone.now()
                    job.save()

                    self.stdout.write(
                        self.style.ERROR(f"Processing job {job.id} failed: {str(e)}")
                    )
                    raise CommandError(f"Error: {str(e)}")

        except Tenant.DoesNotExist:
            raise CommandError(f"Tenant with slug {tenant_slug} does not exist")

        except DocumentSource.DoesNotExist:
            raise CommandError(
                f"Document source with ID {source_id} does not exist for tenant {tenant_slug}"
            )

        except User.DoesNotExist:
            raise CommandError(f"User with username {username} does not exist")

    def _create_processing_job(self, tenant, source, user):
        """
        Create a processing job with retry logic for database contention.

        Args:
            tenant: Tenant object
            source: DocumentSource object
            user: User object

        Returns:
            DocumentProcessingJob: Created job
        """
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                with transaction.atomic():
                    # Check for existing pending or processing jobs for this source
                    existing_job = DocumentProcessingJob.objects.filter(
                        source=source,
                        tenant=tenant,
                        status__in=["pending", "processing"],
                    ).first()

                    if existing_job:
                        self.stdout.write(
                            self.style.WARNING(
                                f"Found existing job {existing_job.id} for source {source.name} with status {existing_job.status}"
                            )
                        )
                        return existing_job

                    # Create a new job
                    job = DocumentProcessingJob.objects.create(
                        source=source, tenant=tenant, status="pending", created_by=user
                    )

                    return job

            except (IntegrityError, DatabaseError):
                retry_count += 1
                if retry_count >= max_retries:
                    raise

                # Wait before retrying
                time.sleep(1)

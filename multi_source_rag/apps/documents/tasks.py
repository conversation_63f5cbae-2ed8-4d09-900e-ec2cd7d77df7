"""
Celery tasks for the documents app.
"""

import logging
from datetime import timedelta

from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource
from apps.documents.services.data_integrity import DataIntegrityService
from apps.documents.services.llama_ingestion_service_unified import IngestionService
from celery import shared_task
from django.utils import timezone

logger = logging.getLogger(__name__)


@shared_task
def sync_document_source(source_id, tenant_id=None, days=None):
    """
    Sync a document source.

    Args:
        source_id: ID of the document source
        tenant_id: ID of the tenant (optional if source has tenant)
        days: Number of days to fetch (optional)
    """
    logger.info(f"Starting sync for source {source_id}")

    try:
        # Initialize ingestion service
        ingestion_service = IngestionService(tenant_id=tenant_id)

        # Prepare parameters
        kwargs = {}
        if days:
            kwargs["days"] = days

        # Sync source
        processed, failed = ingestion_service.sync_source_from_last_update(
            source_id=source_id, **kwargs
        )

        logger.info(
            f"Sync completed for source {source_id}: {processed} processed, {failed} failed"
        )
        return {"status": "success", "processed": processed, "failed": failed}

    except Exception as e:
        logger.error(f"Error syncing source {source_id}: {str(e)}")
        return {"status": "error", "error": str(e)}


@shared_task
def verify_data_integrity(tenant_id=None, source_id=None, auto_repair=False):
    """
    Verify data integrity across databases and tables.

    Args:
        tenant_id: ID of the tenant (required)
        source_id: ID of the source to check (optional)
        auto_repair: Whether to automatically repair issues (default: False)
    """
    logger.info(f"Starting data integrity verification for tenant {tenant_id}")

    try:
        # Get tenant
        try:
            tenant = Tenant.objects.get(id=tenant_id)
        except Tenant.DoesNotExist:
            logger.error(f"Tenant with ID {tenant_id} not found")
            return {"status": "error", "error": f"Tenant with ID {tenant_id} not found"}

        # Initialize data integrity service
        service = DataIntegrityService(tenant)

        # Run verification or repair
        if auto_repair:
            results = service.repair_data_integrity(
                source_id=source_id, auto_repair=True
            )
            logger.info(f"Data integrity repair completed for tenant {tenant_id}")
        else:
            results = service.verify_data_integrity(source_id=source_id)
            logger.info(f"Data integrity verification completed for tenant {tenant_id}")

        return results

    except Exception as e:
        logger.error(
            f"Error during data integrity check for tenant {tenant_id}: {str(e)}"
        )
        return {"status": "error", "error": str(e)}


@shared_task
def scheduled_data_integrity_check():
    """
    Run scheduled data integrity checks for all tenants.

    This task:
    1. Runs verification for all tenants
    2. Automatically repairs critical issues
    3. Logs results for monitoring
    """
    logger.info("Starting scheduled data integrity checks")

    results = {
        "tenants_checked": 0,
        "tenants_with_issues": 0,
        "total_issues": 0,
        "auto_repaired": 0,
        "tenant_results": [],
    }

    # Get all tenants
    tenants = Tenant.objects.all()
    results["tenants_checked"] = tenants.count()

    for tenant in tenants:
        try:
            # First verify to identify issues
            service = DataIntegrityService(tenant)
            verification = service.verify_data_integrity()

            # Count issues
            total_issues = sum(verification["issues"].values())

            if total_issues > 0:
                results["tenants_with_issues"] += 1
                results["total_issues"] += total_issues

                # Auto-repair critical issues
                critical_issues = (
                    verification["issues"]["chunks_without_embeddings"]
                    + verification["issues"]["orphaned_embeddings"]
                    + verification["issues"]["broken_relationships"]
                )

                if critical_issues > 0:
                    repair_results = service.repair_data_integrity(auto_repair=True)
                    results["auto_repaired"] += sum(repair_results["repaired"].values())

            # Add tenant result
            results["tenant_results"].append(
                {
                    "tenant_id": str(tenant.id),
                    "tenant_name": tenant.name,
                    "issues_found": total_issues,
                    "verification": verification,
                }
            )

        except Exception as e:
            logger.error(f"Error checking tenant {tenant.name}: {str(e)}")
            results["tenant_results"].append(
                {
                    "tenant_id": str(tenant.id),
                    "tenant_name": tenant.name,
                    "error": str(e),
                }
            )

    logger.info(
        f"Scheduled data integrity checks completed: "
        f"{results['tenants_with_issues']}/{results['tenants_checked']} "
        f"tenants had issues, {results['auto_repaired']} issues auto-repaired"
    )

    return results

#!/usr/bin/env python3
"""
Production-grade data ingestion script.
Uses the same services and patterns as production environment.
"""

import os
import sys
import django
import time
import json
from datetime import datetime
from typing import Dict, Any, List

# Setup Django
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import DocumentSource, DocumentProcessingJob
from apps.documents.services.ingestion_service import IngestionService

def get_or_create_user() -> User:
    """Get or create the ingestion user."""
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'username': '<EMAIL>',
            'first_name': 'Mahesh',
            'is_active': True
        }
    )
    if created:
        print(f"✅ Created user: {user.email}")
    else:
        print(f"✅ Using existing user: {user.email}")
    return user

def get_tenant() -> Tenant:
    """Get the tenant for ingestion."""
    tenant = Tenant.objects.first()
    if not tenant:
        raise ValueError("No tenant found. Please create a tenant first.")
    print(f"✅ Using tenant: {tenant.name} ({tenant.slug})")
    return tenant

def get_available_sources(tenant: Tenant) -> List[DocumentSource]:
    """Get all available document sources."""
    sources = DocumentSource.objects.filter(tenant=tenant)
    print(f"\n📚 AVAILABLE DOCUMENT SOURCES:")
    print("-" * 40)
    
    if not sources.exists():
        print("   No document sources found.")
        return []
    
    for i, source in enumerate(sources, 1):
        print(f"   {i}. {source.name} ({source.source_type})")
        print(f"      Status: {source.status}")
        if hasattr(source, 'last_sync_at') and source.last_sync_at:
            print(f"      Last sync: {source.last_sync_at}")
    
    return list(sources)

def ingest_source(source: DocumentSource, user: User, tenant: Tenant) -> Dict[str, Any]:
    """Ingest a single document source using production services."""
    print(f"\n🚀 INGESTING SOURCE: {source.name}")
    print("-" * 50)
    
    # Initialize the production ingestion service
    ingestion_service = IngestionService(tenant=tenant, user=user)
    
    # Create processing job (like production)
    job = DocumentProcessingJob.objects.create(
        tenant=tenant,
        source=source,
        status="pending",
        created_by=user,
        metadata={
            "script_run": True,
            "timestamp": datetime.now().isoformat(),
            "source_type": source.source_type
        }
    )
    
    print(f"✅ Created processing job: {job.id}")
    
    start_time = time.time()
    
    try:
        # Update job status
        job.status = "processing"
        job.save()
        
        # Process the source (production method)
        processed_count, failed_count = ingestion_service.process_source(
            source=source,
            job=job,
            batch_size=50  # Production batch size
        )
        
        processing_time = time.time() - start_time
        
        # Get processing stats
        stats = ingestion_service.get_processing_stats()
        
        # Update job with results
        job.status = "completed" if failed_count == 0 else "completed_with_errors"
        job.documents_processed = processed_count
        job.documents_failed = failed_count
        job.processing_time_seconds = processing_time
        job.metadata.update({
            "processing_stats": stats,
            "completed_at": datetime.now().isoformat()
        })
        job.save()
        
        # Print results
        print(f"✅ Processing completed!")
        print(f"   Processed: {processed_count:,} documents")
        print(f"   Failed: {failed_count:,} documents")
        print(f"   Time: {processing_time:.2f} seconds")
        print(f"   Rate: {processed_count/processing_time:.1f} docs/sec" if processing_time > 0 else "   Rate: N/A")
        
        if stats:
            print(f"   Chunks created: {stats.get('chunks_created', 0):,}")
            print(f"   Documents updated: {stats.get('documents_updated', 0):,}")
        
        return {
            "success": True,
            "processed": processed_count,
            "failed": failed_count,
            "processing_time": processing_time,
            "stats": stats,
            "job_id": job.id
        }
        
    except Exception as e:
        processing_time = time.time() - start_time
        
        # Update job with error
        job.status = "failed"
        job.error_message = str(e)
        job.processing_time_seconds = processing_time
        job.metadata.update({
            "error_at": datetime.now().isoformat(),
            "error_details": str(e)
        })
        job.save()
        
        print(f"❌ Processing failed: {e}")
        
        return {
            "success": False,
            "error": str(e),
            "processing_time": processing_time,
            "job_id": job.id
        }

def ingest_all_sources(tenant: Tenant, user: User) -> Dict[str, Any]:
    """Ingest all available sources."""
    sources = get_available_sources(tenant)
    
    if not sources:
        print("❌ No sources available for ingestion.")
        return {"success": False, "error": "No sources available"}
    
    print(f"\n🔄 INGESTING ALL {len(sources)} SOURCES")
    print("=" * 60)
    
    results = {}
    total_processed = 0
    total_failed = 0
    total_time = 0
    
    for source in sources:
        result = ingest_source(source, user, tenant)
        results[source.name] = result
        
        if result["success"]:
            total_processed += result["processed"]
            total_failed += result["failed"]
        
        total_time += result["processing_time"]
        
        # Brief pause between sources
        time.sleep(1)
    
    # Summary
    print(f"\n📊 INGESTION SUMMARY")
    print("=" * 60)
    print(f"   Sources processed: {len(sources)}")
    print(f"   Total documents: {total_processed:,}")
    print(f"   Total failures: {total_failed:,}")
    print(f"   Total time: {total_time:.2f} seconds")
    print(f"   Overall rate: {total_processed/total_time:.1f} docs/sec" if total_time > 0 else "   Overall rate: N/A")
    
    success_rate = (total_processed / (total_processed + total_failed) * 100) if (total_processed + total_failed) > 0 else 0
    print(f"   Success rate: {success_rate:.1f}%")
    
    return {
        "success": True,
        "sources_processed": len(sources),
        "total_processed": total_processed,
        "total_failed": total_failed,
        "total_time": total_time,
        "success_rate": success_rate,
        "results": results
    }

def ingest_specific_source(source_name: str, tenant: Tenant, user: User) -> Dict[str, Any]:
    """Ingest a specific source by name."""
    try:
        source = DocumentSource.objects.get(tenant=tenant, name=source_name)
        return ingest_source(source, user, tenant)
    except DocumentSource.DoesNotExist:
        print(f"❌ Source '{source_name}' not found.")
        available_sources = DocumentSource.objects.filter(tenant=tenant).values_list('name', flat=True)
        print(f"Available sources: {list(available_sources)}")
        return {"success": False, "error": f"Source '{source_name}' not found"}

def main():
    """Main ingestion function."""
    print("🚀 PRODUCTION-GRADE DATA INGESTION")
    print("=" * 60)
    
    # Setup
    try:
        user = get_or_create_user()
        tenant = get_tenant()
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return False
    
    # Get available sources
    sources = get_available_sources(tenant)
    
    if not sources:
        print("\n❌ No document sources available.")
        print("Please create document sources first using Django admin or management commands.")
        return False
    
    # Interactive selection
    print(f"\n🎯 INGESTION OPTIONS:")
    print("   1. Ingest all sources")
    print("   2. Ingest specific source")
    print("   3. Exit")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        # Ingest all sources
        result = ingest_all_sources(tenant, user)
        return result["success"]
    
    elif choice == "2":
        # Ingest specific source
        print(f"\nAvailable sources:")
        for i, source in enumerate(sources, 1):
            print(f"   {i}. {source.name}")
        
        try:
            source_choice = int(input(f"\nSelect source (1-{len(sources)}): ")) - 1
            if 0 <= source_choice < len(sources):
                selected_source = sources[source_choice]
                result = ingest_source(selected_source, user, tenant)
                return result["success"]
            else:
                print("❌ Invalid selection.")
                return False
        except ValueError:
            print("❌ Invalid input.")
            return False
    
    elif choice == "3":
        print("👋 Exiting...")
        return True
    
    else:
        print("❌ Invalid option.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

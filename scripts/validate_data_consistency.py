#!/usr/bin/env python3
"""
Production-grade data consistency validation script.
Validates integrity between Django database and Qdrant vector database.
"""

import os
import sys
import django
import requests
import json
from datetime import datetime
from typing import Dict, Any, List, Optional

# Setup Django
project_root = os.path.join(os.path.dirname(__file__), '..', 'multi_source_rag')
sys.path.insert(0, project_root)
os.chdir(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.conf import settings
from django.contrib.auth.models import User
from apps.accounts.models import Tenant
from apps.documents.models import RawDocument, DocumentChunk, DocumentSource, EmbeddingMetadata
from apps.search.models import SearchResult, SearchQuery, ResultCitation
from apps.documents.services.ingestion_service import IngestionService

def get_qdrant_info() -> Dict[str, Any]:
    """Get Qdrant cluster information."""
    qdrant_host = getattr(settings, 'QDRANT_HOST', 'localhost')
    qdrant_port = getattr(settings, 'QDRANT_PORT', 6333)
    qdrant_url = f"http://{qdrant_host}:{qdrant_port}"
    
    try:
        # Get cluster info
        response = requests.get(f"{qdrant_url}/cluster")
        cluster_info = response.json() if response.status_code == 200 else {}
        
        # Get collections
        response = requests.get(f"{qdrant_url}/collections")
        collections_info = response.json() if response.status_code == 200 else {}
        
        collections = collections_info.get('result', {}).get('collections', [])
        
        return {
            "status": "connected",
            "url": qdrant_url,
            "cluster_info": cluster_info,
            "collections": collections,
            "collection_count": len(collections)
        }
        
    except requests.exceptions.ConnectionError:
        return {
            "status": "disconnected",
            "url": qdrant_url,
            "error": "Cannot connect to Qdrant"
        }
    except Exception as e:
        return {
            "status": "error",
            "url": qdrant_url,
            "error": str(e)
        }

def validate_django_database() -> Dict[str, Any]:
    """Validate Django database integrity."""
    print("🔍 VALIDATING DJANGO DATABASE")
    print("-" * 40)
    
    # Get counts
    counts = {
        'tenants': Tenant.objects.count(),
        'users': User.objects.count(),
        'document_sources': DocumentSource.objects.count(),
        'raw_documents': RawDocument.objects.count(),
        'document_chunks': DocumentChunk.objects.count(),
        'embedding_metadata': EmbeddingMetadata.objects.count(),
        'search_queries': SearchQuery.objects.count(),
        'search_results': SearchResult.objects.count(),
        'result_citations': ResultCitation.objects.count(),
    }
    
    print("📊 Database counts:")
    for model, count in counts.items():
        print(f"   {model}: {count:,}")
    
    # Check for orphaned records
    orphaned_chunks = DocumentChunk.objects.filter(document__isnull=True).count()
    orphaned_embeddings = EmbeddingMetadata.objects.filter(chunk__isnull=True).count()
    orphaned_citations = ResultCitation.objects.filter(result__isnull=True).count()
    
    orphans = {
        'orphaned_chunks': orphaned_chunks,
        'orphaned_embeddings': orphaned_embeddings,
        'orphaned_citations': orphaned_citations
    }
    
    print("\n🔗 Orphaned records:")
    for orphan_type, count in orphans.items():
        status = "✅" if count == 0 else "❌"
        print(f"   {status} {orphan_type}: {count:,}")
    
    # Check embedding consistency
    total_chunks = counts['document_chunks']
    total_embeddings = counts['embedding_metadata']
    embedding_coverage = (total_embeddings / total_chunks * 100) if total_chunks > 0 else 0
    
    print(f"\n📈 Embedding coverage: {embedding_coverage:.1f}%")
    
    issues = []
    if orphaned_chunks > 0:
        issues.append(f"{orphaned_chunks} orphaned chunks")
    if orphaned_embeddings > 0:
        issues.append(f"{orphaned_embeddings} orphaned embeddings")
    if orphaned_citations > 0:
        issues.append(f"{orphaned_citations} orphaned citations")
    if embedding_coverage < 95:
        issues.append(f"Low embedding coverage: {embedding_coverage:.1f}%")
    
    return {
        "counts": counts,
        "orphans": orphans,
        "embedding_coverage": embedding_coverage,
        "issues": issues,
        "is_healthy": len(issues) == 0
    }

def validate_qdrant_database() -> Dict[str, Any]:
    """Validate Qdrant vector database."""
    print("\n🔍 VALIDATING QDRANT DATABASE")
    print("-" * 40)
    
    qdrant_info = get_qdrant_info()
    
    if qdrant_info["status"] != "connected":
        print(f"❌ Qdrant connection failed: {qdrant_info.get('error', 'Unknown error')}")
        return {
            "status": qdrant_info["status"],
            "error": qdrant_info.get("error"),
            "is_healthy": False
        }
    
    print(f"✅ Connected to Qdrant: {qdrant_info['url']}")
    print(f"📊 Collections found: {qdrant_info['collection_count']}")
    
    collections_detail = []
    total_vectors = 0
    
    for collection in qdrant_info["collections"]:
        collection_name = collection["name"]
        
        try:
            # Get collection info
            qdrant_url = qdrant_info["url"]
            response = requests.get(f"{qdrant_url}/collections/{collection_name}")
            
            if response.status_code == 200:
                collection_info = response.json()
                vector_count = collection_info.get("result", {}).get("vectors_count", 0)
                total_vectors += vector_count
                
                collections_detail.append({
                    "name": collection_name,
                    "vector_count": vector_count,
                    "status": "healthy"
                })
                
                print(f"   ✅ {collection_name}: {vector_count:,} vectors")
            else:
                collections_detail.append({
                    "name": collection_name,
                    "status": "error",
                    "error": f"HTTP {response.status_code}"
                })
                print(f"   ❌ {collection_name}: Error getting info")
                
        except Exception as e:
            collections_detail.append({
                "name": collection_name,
                "status": "error",
                "error": str(e)
            })
            print(f"   ❌ {collection_name}: {e}")
    
    print(f"\n📈 Total vectors: {total_vectors:,}")
    
    return {
        "status": "connected",
        "url": qdrant_info["url"],
        "collection_count": qdrant_info["collection_count"],
        "collections": collections_detail,
        "total_vectors": total_vectors,
        "is_healthy": all(c.get("status") == "healthy" for c in collections_detail)
    }

def validate_cross_database_consistency() -> Dict[str, Any]:
    """Validate consistency between Django and Qdrant with deep reference validation."""
    print("\n🔍 VALIDATING CROSS-DATABASE CONSISTENCY")
    print("-" * 40)

    # Get Django embedding metadata
    django_embeddings = EmbeddingMetadata.objects.all()
    django_vector_ids = set(django_embeddings.values_list('vector_id', flat=True))
    django_count = len(django_vector_ids)

    print(f"📊 Django embedding metadata: {django_count:,}")

    # Get Qdrant info
    qdrant_info = get_qdrant_info()

    if qdrant_info["status"] != "connected":
        return {
            "error": "Cannot connect to Qdrant for consistency check",
            "is_consistent": False
        }

    qdrant_total_vectors = sum(
        c.get("vector_count", 0) for c in qdrant_info.get("collections", [])
        if c.get("status") == "healthy"
    )

    print(f"📊 Qdrant total vectors: {qdrant_total_vectors:,}")

    # Deep reference validation
    reference_validation = validate_vector_references(django_embeddings, qdrant_info)

    # Calculate consistency metrics
    vector_count_diff = abs(django_count - qdrant_total_vectors)
    consistency_ratio = min(django_count, qdrant_total_vectors) / max(django_count, qdrant_total_vectors) if max(django_count, qdrant_total_vectors) > 0 else 1

    print(f"📈 Vector count difference: {vector_count_diff:,}")
    print(f"📈 Consistency ratio: {consistency_ratio:.3f}")

    # Print reference validation results
    print(f"🔗 Reference validation:")
    print(f"   Vectors checked: {reference_validation['vectors_checked']:,}")
    print(f"   Found in Qdrant: {reference_validation['found_in_qdrant']:,}")
    print(f"   Missing from Qdrant: {reference_validation['missing_from_qdrant']:,}")
    print(f"   Orphaned in Qdrant: {reference_validation['orphaned_in_qdrant']:,}")
    print(f"   Reference accuracy: {reference_validation['reference_accuracy']:.1%}")

    # Determine if consistent (stricter criteria with reference validation)
    is_consistent = (
        consistency_ratio >= 0.95 and  # Within 5% of each other
        vector_count_diff <= max(django_count, qdrant_total_vectors) * 0.05 and  # Less than 5% difference
        reference_validation['reference_accuracy'] >= 0.95  # 95% of references must be valid
    )

    status = "✅ Consistent" if is_consistent else "❌ Inconsistent"
    print(f"🎯 Status: {status}")

    return {
        "django_count": django_count,
        "qdrant_count": qdrant_total_vectors,
        "count_difference": vector_count_diff,
        "consistency_ratio": consistency_ratio,
        "reference_validation": reference_validation,
        "is_consistent": is_consistent
    }

def validate_source_specific_consistency(tenant: Tenant) -> Dict[str, Any]:
    """Validate consistency for each document source."""
    print("\n🔍 VALIDATING SOURCE-SPECIFIC CONSISTENCY")
    print("-" * 40)
    
    sources = DocumentSource.objects.filter(tenant=tenant)
    source_results = {}
    
    # Initialize ingestion service for validation
    user = User.objects.first()
    ingestion_service = IngestionService(tenant, user)
    
    for source in sources:
        print(f"\n📚 Source: {source.name}")
        
        # Use the new validation method from our improved ingestion service
        validation_result = ingestion_service.validate_data_consistency(source)
        source_results[source.name] = validation_result
        
        # Print results
        print(f"   Total embeddings: {validation_result['total_embeddings']:,}")
        print(f"   Synced embeddings: {validation_result['synced_embeddings']:,}")
        print(f"   Orphaned embeddings: {validation_result['orphaned_embeddings']:,}")
        print(f"   Consistency score: {validation_result['consistency_score']:.1f}%")
        
        status = "✅ Consistent" if validation_result['is_consistent'] else "❌ Inconsistent"
        print(f"   Status: {status}")
    
    # Overall source consistency
    total_sources = len(source_results)
    consistent_sources = sum(1 for r in source_results.values() if r['is_consistent'])
    overall_consistency = consistent_sources / total_sources if total_sources > 0 else 1
    
    print(f"\n📊 Overall source consistency: {consistent_sources}/{total_sources} ({overall_consistency:.1%})")
    
    return {
        "total_sources": total_sources,
        "consistent_sources": consistent_sources,
        "overall_consistency": overall_consistency,
        "source_results": source_results,
        "is_healthy": overall_consistency >= 0.9
    }

def generate_validation_report(results: Dict[str, Any]) -> str:
    """Generate a comprehensive validation report."""
    timestamp = datetime.now().isoformat()
    
    report = {
        "timestamp": timestamp,
        "validation_results": results,
        "summary": {
            "django_healthy": results["django"]["is_healthy"],
            "qdrant_healthy": results["qdrant"]["is_healthy"],
            "cross_db_consistent": results["cross_db"]["is_consistent"],
            "sources_healthy": results["sources"]["is_healthy"],
            "overall_healthy": all([
                results["django"]["is_healthy"],
                results["qdrant"]["is_healthy"],
                results["cross_db"]["is_consistent"],
                results["sources"]["is_healthy"]
            ])
        }
    }
    
    # Save report
    report_filename = f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    return report_filename

def main():
    """Main validation function."""
    print("🔍 PRODUCTION-GRADE DATA CONSISTENCY VALIDATION")
    print("=" * 60)
    
    # Get tenant
    tenant = Tenant.objects.first()
    if not tenant:
        print("❌ No tenant found.")
        return False
    
    print(f"✅ Validating tenant: {tenant.name}")
    
    # Run all validations
    results = {}
    
    # 1. Django database validation
    results["django"] = validate_django_database()
    
    # 2. Qdrant database validation
    results["qdrant"] = validate_qdrant_database()
    
    # 3. Cross-database consistency
    results["cross_db"] = validate_cross_database_consistency()
    
    # 4. Source-specific consistency
    results["sources"] = validate_source_specific_consistency(tenant)
    
    # Generate report
    report_file = generate_validation_report(results)
    
    # Final summary
    print(f"\n🎯 VALIDATION SUMMARY")
    print("=" * 60)
    print(f"   Django database: {'✅ Healthy' if results['django']['is_healthy'] else '❌ Issues found'}")
    print(f"   Qdrant database: {'✅ Healthy' if results['qdrant']['is_healthy'] else '❌ Issues found'}")
    print(f"   Cross-DB consistency: {'✅ Consistent' if results['cross_db']['is_consistent'] else '❌ Inconsistent'}")
    print(f"   Source consistency: {'✅ Healthy' if results['sources']['is_healthy'] else '❌ Issues found'}")
    
    overall_healthy = all([
        results["django"]["is_healthy"],
        results["qdrant"]["is_healthy"],
        results["cross_db"]["is_consistent"],
        results["sources"]["is_healthy"]
    ])
    
    print(f"\n🏆 OVERALL STATUS: {'✅ HEALTHY' if overall_healthy else '❌ ISSUES DETECTED'}")
    print(f"📄 Report saved: {report_file}")
    
    return overall_healthy

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
